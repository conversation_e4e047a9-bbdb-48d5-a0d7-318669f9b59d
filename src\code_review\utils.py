import os

def get_env_bool(key: str, default: bool = False) -> bool:
    """Get boolean value from environment variable."""
    value = os.getenv(key, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')


def get_env_int(key: str, default: int = 0) -> int:
    """Get integer value from environment variable."""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        return default


def get_env_float(key: str, default: float = 0.0) -> float:
    """Get float value from environment variable."""
    try:
        return float(os.getenv(key, str(default)))
    except ValueError:
        return default


def get_parallel_config() -> dict:
    """Get parallel processing configuration from environment variables."""
    max_workers = get_env_int("PARALLEL_MAX_WORKERS", 0)
    return {
        "enabled": get_env_bool("PARALLEL_ENABLED", True),
        "max_workers": max_workers if max_workers > 0 else None,
        "agent_timeout": get_env_float("PARALLEL_AGENT_TIMEOUT", 120.0),
        "fail_fast": get_env_bool("PARALLEL_FAIL_FAST", False),
    }


def get_model_config() -> dict:
    """Get model configuration from environment variables."""
    return {
        "model_path": os.getenv("MODEL_PATH", "./models/DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf"),
        "context_window": get_env_int("MODEL_CONTEXT_WINDOW", 4096),
        "temperature": get_env_float("MODEL_TEMPERATURE", 0.1),
        "max_tokens": get_env_int("MODEL_MAX_TOKENS", 512),
        "threads": get_env_int("MODEL_THREADS", 2),
    }


def get_vector_config() -> dict:
    """Get vector store configuration from environment variables."""
    return {
        "embedding_model": os.getenv("EMBEDDING_MODEL", "./models/all-MiniLM-L6-v2-Q4_K_M.gguf"),
        "chunk_size": get_env_int("CHUNK_SIZE", 1000),
        "chunk_overlap": get_env_int("CHUNK_OVERLAP", 200),
    }


def get_default_language() -> str:
    """Get default programming language from environment."""
    return os.getenv("DEFAULT_LANGUAGE", "CSharp")


def truncate_text(text: str, max_tokens: int = 3000) -> str:
    """
    Truncate text to fit within token limit.
    Rough estimation: 1 token ≈ 4 characters for English text.
    """
    if not text:
        return text

    # Rough token estimation (4 chars per token)
    max_chars = max_tokens * 4

    if len(text) <= max_chars:
        return text

    # Truncate and add indicator
    truncated = text[:max_chars - 100]  # Leave space for truncation message

    # Try to cut at a reasonable boundary (line break)
    last_newline = truncated.rfind('\n')
    if last_newline > max_chars * 0.8:  # If we can cut at 80% or more, do it
        truncated = truncated[:last_newline]

    return truncated + "\n\n... [TRUNCATED - Content too long for context window] ..."


def get_diff_summary(diff_text: str, max_lines: int = 50) -> str:
    """
    Create a summary of the diff showing key changes.
    """
    if not diff_text:
        return ""

    lines = diff_text.split('\n')

    if len(lines) <= max_lines:
        return diff_text

    # Extract key information
    added_lines = [line for line in lines if line.startswith('+') and not line.startswith('+++')]
    removed_lines = [line for line in lines if line.startswith('-') and not line.startswith('---')]
    file_headers = [line for line in lines if line.startswith('@@') or line.startswith('diff')]

    summary_lines = []
    summary_lines.extend(file_headers[:10])  # Max 10 file headers
    summary_lines.append(f"\n# Added lines ({len(added_lines)} total):")
    summary_lines.extend(added_lines[:15])  # Max 15 added lines
    if len(added_lines) > 15:
        summary_lines.append("... [more added lines] ...")

    summary_lines.append(f"\n# Removed lines ({len(removed_lines)} total):")
    summary_lines.extend(removed_lines[:15])  # Max 15 removed lines
    if len(removed_lines) > 15:
        summary_lines.append("... [more removed lines] ...")

    return '\n'.join(summary_lines)
