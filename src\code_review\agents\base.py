import os
import asyncio
from abc import ABC, abstractmethod
from typing import Optional, Any
from langchain_community.chat_models import ChatLlamaCpp
from langchain_core.messages import SystemMessage, HumanMessage

_cached_llm = None

def load_local_llm():
    """Load and cache the local LLM instance."""
    global _cached_llm
    if _cached_llm is not None:
        return _cached_llm

    model_path = os.getenv("MODEL_PATH", "./models/DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model file not found at {model_path}")

    try:
        _cached_llm = ChatLlamaCpp(
            model_path=model_path,
            n_ctx=int(os.getenv("MODEL_CONTEXT_WINDOW", "8192")),  # Increased from 4096
            n_threads=int(os.getenv("MODEL_THREADS", "2")),
            temperature=float(os.getenv("MODEL_TEMPERATURE", "0.1")),
            max_tokens=int(os.getenv("MODEL_MAX_TOKENS", "512")),
            verbose=False,  # Disable verbose logging
            stop=["</s>", "\n\n"],  # Stop sequences
        )
        return _cached_llm
    except Exception as e:
        raise RuntimeError(f"Failed to load LLM model: {str(e)}")


class BaseAgent(ABC):
    """Base class for all code review agents."""
    
    def __init__(self, llm: Optional[Any] = None):
        """Initialize the agent with an optional LLM instance."""
        self.llm = llm or load_local_llm()
    
    @property
    @abstractmethod
    def system_prompt(self) -> str:
        """Return the system prompt specific to this agent."""
        pass
    
    def invoke(self, user_prompt: str) -> str:
        """Invoke the agent with a user prompt."""
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=user_prompt)
        ]
        response = self.llm.invoke(messages)
        return response.content if hasattr(response, 'content') else str(response)

    async def ainvoke(self, user_prompt: str) -> str:
        """Async invoke the agent with a user prompt."""
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=user_prompt)
        ]
        # Check if LLM has async support
        if hasattr(self.llm, 'ainvoke'):
            response = await self.llm.ainvoke(messages)
        else:
            # Fallback to sync invoke in thread pool
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, self.llm.invoke, messages)
        return response.content if hasattr(response, 'content') else str(response)

    @abstractmethod
    def process(self, **kwargs) -> dict:
        """Process the input and return structured output."""
        pass

    async def aprocess(self, **kwargs) -> dict:
        """Async process the input and return structured output."""
        # Default implementation runs sync process in thread pool
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.process, **kwargs)
